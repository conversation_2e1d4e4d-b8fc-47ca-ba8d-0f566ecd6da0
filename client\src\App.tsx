import { Routes, Route, useParams } from "react-router-dom";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
// import Blog from "@/pages/blog";
import BlogPost from "@/pages/blog-post";
import PrivacyPolicy from "@/pages/privacy-policy";
import TermsOfService from "@/pages/terms-of-service";
import FAQ from "@/pages/faq";
import ContactUs from "@/pages/contact-us";

import Header from "@/components/Header";
import SimpleFooter from "@/components/SimpleFooter";
import AppWrapper from "@/components/AppWrapper";
import { HelmetProvider } from 'react-helmet-async';

// 临时内联Blog组件用于测试
const Blog = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-8">Blog Works!</h1>
      <p>This is a working blog page.</p>
    </div>
  );
};

// 语言路由包装组件
function LangHomeRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <Home />;
}

function LangBlogRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <Blog />;
}

function LangBlogPostRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <BlogPost />;
}

function LangPrivacyRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <PrivacyPolicy />;
}

function LangTermsRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <TermsOfService />;
}

function LangFaqRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <FAQ />;
}

function LangContactRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <ContactUs />;
}



function Router() {
  return (
    <Routes>
      {/* 新增页面路由 - 最高优先级 */}
      <Route path="/test-new-page" element={<div><h1>Test New Page</h1><p>This is a test</p></div>} />

      {/* 默认路由（英文） - 具体路径优先 */}
      <Route path="/" element={<Home />} />
      <Route path="/test-blog" element={<div><h1>Inline Test</h1></div>} />
      <Route path="/blog" element={<Blog />} />
      <Route path="/blog/:slug" element={<BlogPost />} />
      <Route path="/privacy-policy" element={<PrivacyPolicy />} />
      <Route path="/terms-of-service" element={<TermsOfService />} />
      <Route path="/faq" element={<FAQ />} />
      <Route path="/contact-us" element={<ContactUs />} />


      {/* 语言特定路由 - 明确指定语言 */}
      <Route path="/es" element={<LangHomeRoute />} />
      <Route path="/fr" element={<LangHomeRoute />} />
      <Route path="/de" element={<LangHomeRoute />} />
      <Route path="/zh" element={<LangHomeRoute />} />
      <Route path="/es/blog" element={<LangBlogRoute />} />
      <Route path="/fr/blog" element={<LangBlogRoute />} />
      <Route path="/de/blog" element={<LangBlogRoute />} />
      <Route path="/zh/blog" element={<LangBlogRoute />} />
      <Route path="/es/blog/:slug" element={<LangBlogPostRoute />} />
      <Route path="/fr/blog/:slug" element={<LangBlogPostRoute />} />
      <Route path="/de/blog/:slug" element={<LangBlogPostRoute />} />
      <Route path="/zh/blog/:slug" element={<LangBlogPostRoute />} />
      <Route path="/es/privacy-policy" element={<LangPrivacyRoute />} />
      <Route path="/fr/privacy-policy" element={<LangPrivacyRoute />} />
      <Route path="/de/privacy-policy" element={<LangPrivacyRoute />} />
      <Route path="/zh/privacy-policy" element={<LangPrivacyRoute />} />
      <Route path="/es/terms-of-service" element={<LangTermsRoute />} />
      <Route path="/fr/terms-of-service" element={<LangTermsRoute />} />
      <Route path="/de/terms-of-service" element={<LangTermsRoute />} />
      <Route path="/zh/terms-of-service" element={<LangTermsRoute />} />
      <Route path="/es/faq" element={<LangFaqRoute />} />
      <Route path="/fr/faq" element={<LangFaqRoute />} />
      <Route path="/de/faq" element={<LangFaqRoute />} />
      <Route path="/zh/faq" element={<LangFaqRoute />} />
      <Route path="/es/contact-us" element={<LangContactRoute />} />
      <Route path="/fr/contact-us" element={<LangContactRoute />} />
      <Route path="/de/contact-us" element={<LangContactRoute />} />
      <Route path="/zh/contact-us" element={<LangContactRoute />} />


      {/* 404页面 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <HelmetProvider>
        <AppWrapper>
          <TooltipProvider>
            <Toaster />
            <div className="flex flex-col min-h-screen">
              <Header />
              <div className="flex-grow">
                <Router />
              </div>
              <div className="container mx-auto px-4 mb-6">
                <SimpleFooter />
              </div>
            </div>
          </TooltipProvider>
        </AppWrapper>
      </HelmetProvider>
    </QueryClientProvider>
  );
}

export default App;
