import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

const ContactUs: React.FC = () => {
  const { t, i18n } = useTranslation();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        {/* 返回首页链接 */}
        <div className="mb-6">
          <Link
            to={i18n.language === 'en' ? '/' : `/${i18n.language}`}
            className="text-primary hover:text-primary-dark flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('aboutUs.backToHome')}
          </Link>
        </div>

        <h1 className="text-3xl font-bold mb-6">{t('aboutUs.contact.title')}</h1>

        <section>
          <p className="text-gray-700 mb-4">{t('aboutUs.contact.description')}</p>
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <div>
                <span className="font-semibold text-gray-700">{t('aboutUs.contact.email')}: </span>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-primary hover:text-primary-dark font-medium"
                >
                  {t('aboutUs.contact.emailValue')}
                </a>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ContactUs;
