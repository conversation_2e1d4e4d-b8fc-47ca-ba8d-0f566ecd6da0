// 添加 polyfill 支持旧版浏览器
import 'core-js/stable';
import 'regenerator-runtime/runtime';
import 'whatwg-fetch';
import 'intersection-observer';

import { ViteReactSSG } from 'vite-react-ssg';
import { QueryClientProvider } from "@tanstack/react-query";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import { queryClient } from "./lib/queryClient";
import "./index.css";
import "./lib/i18n"; // 仅初始化 i18n
import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
import Blog from "@/pages/blog";
import BlogPost from "@/pages/blog-post";
import PrivacyPolicy from "@/pages/privacy-policy";
import TermsOfService from "@/pages/terms-of-service";
import FAQ from "@/pages/faq";
import ContactUs from "@/pages/contact-us";
import Header from "@/components/Header";
import SimpleFooter from "@/components/SimpleFooter";
import { RouteObject } from "react-router-dom";

// 导入广告组件
import AdManager from "@/components/AdManager";

// 检测浏览器支持
const checkBrowserSupport = () => {
  if (typeof window === 'undefined') return true;

  // 检测基本功能支持
  const isModernBrowser = (
    'querySelector' in document &&
    'localStorage' in window &&
    'addEventListener' in window &&
    'fetch' in window
  );

  // 如果是旧浏览器，显示提示
  if (!isModernBrowser) {
    const root = document.getElementById('root');
    if (root) {
      root.innerHTML = `
        <div style="padding: 20px; text-align: center; font-family: sans-serif;">
          <h1>浏览器不兼容</h1>
          <p>您的浏览器版本过低，无法正常使用本网站的全部功能。</p>
          <p>请升级到最新版本的 <a href="https://www.google.com/chrome/">Chrome</a>, <a href="https://www.mozilla.org/firefox/">Firefox</a>, <a href="https://www.microsoft.com/edge">Edge</a> 或 <a href="https://www.apple.com/safari/">Safari</a>。</p>
        </div>
      `;
      return false;
    }
  }
  return true;
};

// 创建一个布局组件
const Layout = ({ children }: { children: React.ReactNode }) => {
  console.log('[Layout] 渲染布局组件');

  return (
    <AppWrapper>
      <div className="flex flex-col min-h-screen">
        <Header />

        {/* 顶部横幅广告 */}
        <div className="w-full bg-gray-50 border-b border-gray-200">
          <div className="container mx-auto px-4">
            <AdManager position="top" />
          </div>
        </div>

        <div className="flex-grow">
          {children}
        </div>

        {/* 底部横幅广告 */}
        <div className="w-full bg-gray-50 border-t border-gray-200">
          <div className="container mx-auto px-4">
            <AdManager position="bottom" />
          </div>
        </div>

        <div className="container mx-auto px-4 mb-6">
          <SimpleFooter />
        </div>
      </div>
    </AppWrapper>
  );
};

// 创建一个路由包装函数，用于创建带有语言路径的路由
const createLanguageRoutes = () => {
  // 基础路由配置
  const baseRoutes = [
    {
      path: '/',
      element: <Layout><Home /></Layout>,
      languages: ['en', 'es', 'fr', 'de', 'zh']
    },
    {
      path: '/blog',
      element: <Layout><Blog /></Layout>,
      languages: ['en', 'es', 'fr', 'de', 'zh']
    },
    {
      path: '/blog/:slug',
      element: <Layout><BlogPost /></Layout>,
      languages: ['en', 'es', 'fr', 'de', 'zh']
    },
    {
      path: '/privacy-policy',
      element: <Layout><PrivacyPolicy /></Layout>,
      languages: ['en', 'es', 'fr', 'de', 'zh']
    },
    {
      path: '/terms-of-service',
      element: <Layout><TermsOfService /></Layout>,
      languages: ['en', 'es', 'fr', 'de', 'zh']
    },
    {
      path: '/faq',
      element: <Layout><FAQ /></Layout>,
      languages: ['en', 'es', 'fr', 'de', 'zh']
    },
    {
      path: '/contact-us',
      element: <Layout><ContactUs /></Layout>,
      languages: ['en', 'es', 'fr', 'de', 'zh']
    },
    {
      path: '*',
      element: <Layout><NotFound /></Layout>,
      languages: ['en']
    }
  ];

  // 展开路由，为每种语言创建对应的路由
  const expandedRoutes: RouteObject[] = [];

  baseRoutes.forEach(route => {
    const { path, element, languages } = route;

    // 添加英文路由（默认路由）
    if (languages.includes('en')) {
      expandedRoutes.push({
        path,
        element
      });
    }

    // 添加所有语言的路由（包括英文）
    languages.forEach(lang => {
      // 对于通配符路由，不添加语言前缀
      if (path === '*') return;

      expandedRoutes.push({
        path: `/${lang}${path === '/' ? '' : path}`,
        element
      });
    });
  });

  return expandedRoutes;
};

// 定义路由配置
const routes: RouteObject[] = createLanguageRoutes();

// 创建一个包装组件
const AppWrapper = ({ children }: { children: React.ReactNode }) => {
  // 检查浏览器支持
  if (typeof window !== 'undefined' && !checkBrowserSupport()) {
    return null;
  }

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        {children}
      </TooltipProvider>
    </QueryClientProvider>
  );
};

// 导入 i18n 工厂函数和相关依赖
import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';

// 导入SSGWrapper组件
import SSGWrapper from './components/SSGWrapper';

// 创建一个包装路由的函数，确保每个路由都使用SSGWrapper
const wrapRoutesWithSSG = (originalRoutes: RouteObject[], routePath?: string, isClient?: boolean): RouteObject[] => {
  return originalRoutes.map(route => {
    // 如果有子路由，递归包装
    if (route.children) {
      return {
        ...route,
        children: wrapRoutesWithSSG(route.children, routePath, isClient)
      };
    }

    // 包装当前路由的元素
    if (route.element) {
      return {
        ...route,
        element: (
          <SSGWrapper routePath={routePath} isClient={isClient}>
            {route.element}
          </SSGWrapper>
        )
      };
    }

    return route;
  });
};

// 创建 SSG 应用
export const createRoot = ViteReactSSG(
  { routes },
  ({ routePath, isClient }) => {
    // 浏览器支持检查已经在 AppWrapper 中处理

    // 根据路由路径设置语言
    let lang = 'en';

    if (routePath && routePath.startsWith('/en')) {
      lang = 'en';
    } else if (routePath && routePath.startsWith('/es')) {
      lang = 'es';
    } else if (routePath && routePath.startsWith('/fr')) {
      lang = 'fr';
    } else if (routePath && routePath.startsWith('/de')) {
      lang = 'de';
    } else if (routePath && routePath.startsWith('/zh')) {
      lang = 'zh';
    }

    if (process.env.NODE_ENV !== 'production') {
      console.log(`[SSG] 为路径 ${routePath} 设置语言: ${lang}`);
    }

    // 在SSG过程中，为每个路由创建一个独立的i18n实例
    if (!isClient) {
      // 创建一个新的i18n实例
      const i18nInstance = i18next.createInstance();

      // 初始化实例
      i18nInstance
        .use(initReactI18next)
        .init({
          lng: lang,
          fallbackLng: 'en',
          interpolation: {
            escapeValue: false
          },
          react: {
            useSuspense: false
          }
        });

      // 设置语言
      i18nInstance.changeLanguage(lang);

      // 添加实例标识，方便调试
      (i18nInstance as any).__language_id = `ssg_${lang}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      (i18nInstance as any).__route = routePath;

      // 设置全局变量，供SSGWrapper使用
      (i18next as any).__current_route_instance = i18nInstance;
      (i18next as any).__current_route_language = lang;
      (i18next as any).__current_route_path = routePath;
    }
    // 在客户端，设置初始语言
    else if (typeof window !== 'undefined') {
      window.__INITIAL_LANGUAGE__ = lang;

      // 添加一个事件监听器，在页面加载完成后检查语言
      window.addEventListener('DOMContentLoaded', () => {
        // 从 URL 中获取语言
        const pathParts = window.location.pathname.split('/').filter(Boolean);
        let urlLang = 'en';
        if (pathParts.length > 0) {
          const firstPart = pathParts[0];
          if (['en', 'es', 'fr', 'de', 'zh'].includes(firstPart)) {
            urlLang = firstPart;
          }
        }

        // 确保i18next实例已正确初始化
        if (i18next && i18next.isInitialized) {
          // 如果 URL 中的语言与当前语言不同，则切换语言
          if (urlLang !== i18next.language) {
            console.log(`[客户端] 切换语言从 ${i18next.language} 到 ${urlLang}`);
            i18next.changeLanguage(urlLang);
          }
        } else {
          console.warn('[客户端] i18next实例未初始化，无法切换语言');
        }
      });
    }

    // 注意：我们不能直接修改routes常量，因为它是只读的
    // 但是我们可以在SSGWrapper组件中使用全局变量来传递当前路由的i18n实例
    // 这样，SSGWrapper组件就可以获取到正确的i18n实例
    console.log(`[main.tsx] 为路径 ${routePath} 设置全局i18n实例，语言: ${lang}`);
  },
);
