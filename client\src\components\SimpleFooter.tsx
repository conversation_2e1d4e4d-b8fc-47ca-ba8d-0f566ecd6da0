import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const SimpleFooter = () => {
  const { t, i18n } = useTranslation();
  const currentYear = new Date().getFullYear();

  // 根据当前语言返回相应的文本
  const getLocalizedText = () => {
    switch(i18n.language) {
      case 'en':
        return {
          title: 'Text Case Converter',
          description: 'One-click text case conversion, free and easy to use',
          support: {
            title: 'Support',
            helpCenter: 'Help Center',
            contactUs: 'Contact Us'
          },
          legal: {
            title: 'Legal',
            termsOfService: 'Terms of Service',
            privacyPolicy: 'Privacy Policy'
          },
          about: {
            title: 'About',
            aboutUs: 'About Us',
            faq: 'FAQ'
          },
          copyright: `© 2025 Text Case Converter. All rights reserved.`
        };
      case 'es':
        return {
          title: 'Conversión de Texto',
          description: 'Conversión de mayúsculas y minúsculas con un clic, gratis y fácil de usar',
          support: {
            title: 'Soporte',
            helpCenter: 'Centro de Ayuda',
            contactUs: 'Contáctanos'
          },
          legal: {
            title: 'Legal',
            termsOfService: 'Términos de Servicio',
            privacyPolicy: 'Política de Privacidad'
          },
          about: {
            title: 'Acerca de',
            aboutUs: 'Acerca de Nosotros',
            faq: 'FAQ'
          },
          copyright: `© 2025 Text Case Converter. Todos los derechos reservados.`
        };
      case 'fr':
        return {
          title: 'Conversion de Texte',
          description: 'Conversion de casse de texte en un clic, gratuit et facile à utiliser',
          support: {
            title: 'Support',
            helpCenter: 'Centre d\'Aide',
            contactUs: 'Nous Contacter'
          },
          legal: {
            title: 'Légal',
            termsOfService: 'Conditions de Service',
            privacyPolicy: 'Politique de Confidentialité'
          },
          about: {
            title: 'À Propos',
            aboutUs: 'À Propos de Nous',
            faq: 'FAQ'
          },
          copyright: `© 2025 Text Case Converter. Tous droits réservés.`
        };
      case 'de':
        return {
          title: 'Textumwandlung',
          description: 'Textumwandlung mit einem Klick, kostenlos und einfach zu bedienen',
          support: {
            title: 'Support',
            helpCenter: 'Hilfezentrum',
            contactUs: 'Kontaktieren Sie Uns'
          },
          legal: {
            title: 'Rechtliches',
            termsOfService: 'Nutzungsbedingungen',
            privacyPolicy: 'Datenschutzrichtlinie'
          },
          about: {
            title: 'Über',
            aboutUs: 'Über Uns',
            faq: 'FAQ'
          },
          copyright: `© 2025 Text Case Converter. Alle Rechte vorbehalten.`
        };
      case 'zh':
        return {
          title: 'Text Case Converter',
          description: '一键实现文本大小写多样转换，免费易用',
          support: {
            title: '支持',
            helpCenter: '帮助中心',
            contactUs: '联系我们'
          },
          legal: {
            title: '法律',
            termsOfService: '服务条款',
            privacyPolicy: '隐私政策'
          },
          about: {
            title: '关于',
            aboutUs: '关于我们',
            faq: '常见问题'
          },
          copyright: `© 2025 Text Case Converter. 保留所有权利。`
        };
      default:
        return {
          title: 'Text Case Converter',
          description: 'One-click text case conversion, free and easy to use',
          support: {
            title: 'Support',
            helpCenter: 'Help Center',
            contactUs: 'Contact Us'
          },
          legal: {
            title: 'Legal',
            termsOfService: 'Terms of Service',
            privacyPolicy: 'Privacy Policy'
          },
          about: {
            title: 'About',
            aboutUs: 'About Us',
            faq: 'FAQ'
          },
          copyright: `© 2025 Text Case Converter. All rights reserved.`
        };
    }
  };

  const localizedText = getLocalizedText();

  return (
    <div className="flex flex-col">
      {/* 主要内容和链接区域 */}
      <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-4 sm:mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Logo和描述 */}
          <div className="md:col-span-1">
            <div className="flex items-center mb-2 justify-center md:justify-start">
              <img
                src="/logo.svg"
                alt="Text Case Converter Logo"
                className="w-5 h-5 sm:w-6 sm:h-6 mr-2"
                width="24"
                height="24"
                loading="lazy"
              />
              <h2 className="text-lg sm:text-xl font-bold">{localizedText.title}</h2>
            </div>
            <p className="text-sm sm:text-base text-gray-600 text-center md:text-left">{localizedText.description}</p>
          </div>

          {/* 支持栏 */}
          <div className="text-center md:text-left">
            <h3 className="text-base sm:text-lg font-semibold mb-3 text-gray-800">{localizedText.support.title}</h3>
            <div className="space-y-2">
              <div>
                <Link
                  to={i18n.language === 'en' ? '/faq' : `/${i18n.language}/faq`}
                  className="text-primary hover:text-primary-dark text-sm sm:text-base transition-colors block"
                >
                  {localizedText.support.helpCenter}
                </Link>
              </div>
              <div>
                <Link
                  to={i18n.language === 'en' ? '/contact-us' : `/${i18n.language}/contact-us`}
                  className="text-primary hover:text-primary-dark text-sm sm:text-base transition-colors block"
                >
                  {localizedText.support.contactUs}
                </Link>
              </div>
            </div>
          </div>

          {/* 法律 */}
          <div className="text-center md:text-left">
            <h3 className="text-base sm:text-lg font-semibold mb-3 text-gray-800">{localizedText.legal.title}</h3>
            <div className="space-y-2">
              <div>
                <Link
                  to={i18n.language === 'en' ? '/terms-of-service' : `/${i18n.language}/terms-of-service`}
                  className="text-primary hover:text-primary-dark text-sm sm:text-base transition-colors block"
                >
                  {localizedText.legal.termsOfService}
                </Link>
              </div>
              <div>
                <Link
                  to={i18n.language === 'en' ? '/privacy-policy' : `/${i18n.language}/privacy-policy`}
                  className="text-primary hover:text-primary-dark text-sm sm:text-base transition-colors block"
                >
                  {localizedText.legal.privacyPolicy}
                </Link>
              </div>
            </div>
          </div>


        </div>
      </div>

      {/* 版权信息区域 */}
      <div className="text-center text-gray-600 py-3 sm:py-4 border-t border-gray-200 text-xs sm:text-sm">
        <p>{localizedText.copyright}</p>
      </div>
    </div>
  );
};

export default SimpleFooter;
